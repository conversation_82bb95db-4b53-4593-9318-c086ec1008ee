import { MetadataRoute } from "next";

export default function robots(): MetadataRoute.Robots {
  const baseUrl = process.env.NEXT_PUBLIC_SITE_URL || "https://ai-nav.hnch.pro";

  return {
    rules: [
      {
        userAgent: "*",
        allow: "/",
        disallow: [
          "/api/",
          "/_next/",
          "/admin/",
          "/*.json",
          "/*?*",
          "/uploads/",
        ],
      },
      // 允许搜索引擎访问sitemap文件
      {
        userAgent: "*",
        allow: ["/sitemap*.xml", "/robots.txt"],
      },
    ],
    sitemap: [`${baseUrl}/sitemap.xml`, `${baseUrl}/sitemap-index.xml`],
    // 设置爬取延迟（秒）
    crawlDelay: 1,
  };
}
