import { MetadataRoute } from 'next';

export default function robots(): MetadataRoute.Robots {
  const baseUrl = process.env.NEXT_PUBLIC_SITE_URL || 'https://yourdomain.com';
  
  return {
    rules: {
      userAgent: '*',
      allow: '/',
      disallow: ['/api/', '/_next/', '/*.json', '/*?*'],
    },
    sitemap: `${baseUrl}/sitemap.xml`,
    // 可选：设置爬取延迟（秒）
    // 注意：不是所有搜索引擎都支持此指令
    // crawlDelay: 10,
  };
}
