import { MetadataRoute } from "next";
import prisma from "@/lib/prisma";

// 定义站点地图条目的接口
interface SitemapEntry {
  url: string;
  lastModified?: string | Date;
  changeFrequency?:
    | "always"
    | "hourly"
    | "daily"
    | "weekly"
    | "monthly"
    | "yearly"
    | "never";
  priority?: number;
}

// 获取分类数据
async function getCategories() {
  try {
    const categories = await prisma.category.findMany({
      select: {
        id: true,
        slug: true,
        updatedAt: true,
      },
    });
    return categories;
  } catch (error) {
    console.error("Error fetching categories for sitemap:", error);
    return [];
  }
}

// 获取工具数据
async function getTools() {
  try {
    const tools = await prisma.tool.findMany({
      select: {
        id: true,
        updatedAt: true,
      },
      orderBy: {
        updatedAt: "desc",
      },
    });
    return tools;
  } catch (error) {
    console.error("Error fetching tools for sitemap:", error);
    return [];
  }
}

export default async function sitemap(): Promise<MetadataRoute.Sitemap> {
  const baseUrl = process.env.NEXT_PUBLIC_SITE_URL || "https://ai-nav.hnch.pro";
  const now = new Date();

  // 静态路由
  const staticRoutes: SitemapEntry[] = [
    {
      url: baseUrl,
      lastModified: now,
      changeFrequency: "daily",
      priority: 1.0,
    },
    {
      url: `${baseUrl}/search`,
      lastModified: now,
      changeFrequency: "weekly",
      priority: 0.8,
    },
    {
      url: `${baseUrl}/admin`,
      lastModified: now,
      changeFrequency: "monthly",
      priority: 0.3,
    },
  ];

  try {
    // 动态获取分类和工具数据
    const [categories, tools] = await Promise.all([
      getCategories(),
      getTools(),
    ]);

    // 分类页面
    const categoryRoutes: SitemapEntry[] = categories.map((category) => ({
      url: `${baseUrl}/category/${category.slug || category.id}`,
      lastModified: category.updatedAt || now,
      changeFrequency: "weekly",
      priority: 0.7,
    }));

    // 工具详情页
    const toolRoutes: SitemapEntry[] = tools.map((tool) => ({
      url: `${baseUrl}/tool/${tool.id}`,
      lastModified: tool.updatedAt || now,
      changeFrequency: "daily",
      priority: 0.9,
    }));

    return [
      ...staticRoutes,
      ...categoryRoutes,
      ...toolRoutes,
    ] as MetadataRoute.Sitemap;
  } catch (error) {
    console.error("生成站点地图时出错:", error);
    // 如果发生错误，只返回静态路由
    return staticRoutes as MetadataRoute.Sitemap;
  }
}
